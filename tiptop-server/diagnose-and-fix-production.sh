#!/bin/bash

# TipTop Production Diagnostic and Fix Script
# Run this on GCP Cloud Shell to diagnose and fix the WebSocket server issue

set -e

echo "🔍 TipTop Production Diagnostic and Fix Script"
echo "=============================================="
echo ""

# Function to handle errors
handle_error() {
    echo "❌ Error occurred at line $1"
    exit 1
}

trap 'handle_error $LINENO' ERR

# Get current project
PROJECT_ID=$(gcloud config get-value project)
echo "📋 Current GCP Project: $PROJECT_ID"
echo ""

# Connect to the correct cluster
echo "☸️  Connecting to GKE cluster..."
if gcloud container clusters get-credentials smartparent-k8s --zone=us-central1-f 2>/dev/null; then
    echo "✅ Connected to smartparent-k8s cluster"
elif gcloud container clusters get-credentials smartparent --region=us-central1 2>/dev/null; then
    echo "✅ Connected to smartparent cluster"
else
    echo "❌ Failed to connect to cluster"
    echo "Available clusters:"
    gcloud container clusters list
    exit 1
fi
echo ""

# Check current deployment status
echo "📊 Current TipTop Deployment Status:"
echo "===================================="
echo ""

echo "🔍 Checking pods in tiptop namespace:"
kubectl get pods -n tiptop || echo "❌ No pods found in tiptop namespace"
echo ""

echo "🔍 Checking deployments in tiptop namespace:"
kubectl get deployments -n tiptop || echo "❌ No deployments found in tiptop namespace"
echo ""

echo "🔍 Checking services in tiptop namespace:"
kubectl get services -n tiptop || echo "❌ No services found in tiptop namespace"
echo ""

echo "🔍 Checking ingress in tiptop namespace:"
kubectl get ingress -n tiptop || echo "❌ No ingress found in tiptop namespace"
echo ""

# Check if WebSocket deployment exists
echo "🔍 Checking WebSocket server specifically:"
if kubectl get deployment tiptop-websocket -n tiptop >/dev/null 2>&1; then
    echo "✅ WebSocket deployment exists"
    echo "📊 WebSocket deployment status:"
    kubectl get deployment tiptop-websocket -n tiptop
    echo ""
    echo "📊 WebSocket pods:"
    kubectl get pods -n tiptop -l app=tiptop-websocket
    echo ""
    echo "📊 WebSocket pod logs (last 20 lines):"
    kubectl logs -n tiptop -l app=tiptop-websocket --tail=20 || echo "❌ No logs available"
else
    echo "❌ WebSocket deployment NOT FOUND - This is the main issue!"
    echo ""
    echo "🔧 FIXING: Deploying WebSocket server..."
    
    # Check if secrets exist
    echo "🔍 Checking required secrets..."
    if kubectl get secret tiptop-websocket-secrets -n tiptop >/dev/null 2>&1; then
        echo "✅ WebSocket secrets exist"
    else
        echo "❌ WebSocket secrets missing - applying secrets..."
        kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-secrets.yaml -n tiptop
    fi
    
    # Deploy WebSocket server
    echo "🚀 Deploying WebSocket server..."
    
    # Create temporary deployment file with correct project ID
    sed "s/PROJECT_ID/$PROJECT_ID/g" websocket-server/k8s-deploy/tiptop-websocket-deployment.yaml > /tmp/tiptop-websocket-deployment-temp.yaml
    
    # Apply WebSocket deployment
    kubectl apply -f /tmp/tiptop-websocket-deployment-temp.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-service.yaml -n tiptop
    kubectl apply -f websocket-server/k8s-deploy/tiptop-websocket-ingress.yaml -n tiptop
    
    # Clean up temp file
    rm -f /tmp/tiptop-websocket-deployment-temp.yaml
    
    echo "✅ WebSocket server deployment initiated"
    echo ""
    
    # Wait for deployment to be ready
    echo "⏳ Waiting for WebSocket deployment to be ready (timeout: 5 minutes)..."
    kubectl wait --for=condition=available --timeout=300s deployment/tiptop-websocket -n tiptop || {
        echo "⚠️  Deployment taking longer than expected, checking status..."
        kubectl get pods -n tiptop -l app=tiptop-websocket
        echo ""
        echo "📊 Recent events:"
        kubectl get events -n tiptop --sort-by='.lastTimestamp' | tail -10
    }
fi

echo ""
echo "🔍 Final Status Check:"
echo "====================="
echo ""

echo "📊 All pods in tiptop namespace:"
kubectl get pods -n tiptop
echo ""

echo "📊 All services in tiptop namespace:"
kubectl get services -n tiptop
echo ""

echo "📊 All ingress in tiptop namespace:"
kubectl get ingress -n tiptop
echo ""

# Check WebSocket connectivity
echo "🔍 Testing WebSocket connectivity:"
WEBSOCKET_SERVICE=$(kubectl get service tiptop-websocket -n tiptop -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
if [ -n "$WEBSOCKET_SERVICE" ]; then
    echo "✅ WebSocket service has external IP: $WEBSOCKET_SERVICE"
else
    echo "⚠️  WebSocket service external IP not yet assigned"
    echo "📊 WebSocket service details:"
    kubectl get service tiptop-websocket -n tiptop -o wide
fi
echo ""

# Check ingress status
echo "🔍 Checking ingress status:"
kubectl get ingress -n tiptop -o wide
echo ""

# PostgreSQL access command
echo "🗄️  PostgreSQL Access Command:"
echo "=============================="
echo ""
echo "To access the PostgreSQL database, run this command:"
echo ""
echo "kubectl exec -it -n tiptop deployment/tiptop-postgres -- psql -U \$(kubectl get secret tiptop-postgres-secrets -n tiptop -o jsonpath='{.data.POSTGRES_USER}' | base64 -d) -d \$(kubectl get secret tiptop-postgres-secrets -n tiptop -o jsonpath='{.data.POSTGRES_DB}' | base64 -d)"
echo ""
echo "Or for a simpler one-liner:"
echo "kubectl exec -it -n tiptop deployment/tiptop-postgres -- psql -U postgres -d tiptop"
echo ""

# Check database tables
echo "🔍 Checking database tables:"
echo "kubectl exec -n tiptop deployment/tiptop-postgres -- psql -U postgres -d tiptop -c \"\\dt\""
kubectl exec -n tiptop deployment/tiptop-postgres -- psql -U postgres -d tiptop -c "\\dt" 2>/dev/null || echo "❌ Could not connect to database"
echo ""

# Final recommendations
echo "🎯 Next Steps:"
echo "=============="
echo ""
echo "1. Wait a few minutes for the WebSocket pods to fully start"
echo "2. Check that the extension is connecting to: wss://ws.tiptop.qubitrhythm.com"
echo "3. Test the social features in your browser"
echo "4. If issues persist, check the WebSocket logs with:"
echo "   kubectl logs -n tiptop -l app=tiptop-websocket -f"
echo ""
echo "🔍 To monitor the deployment:"
echo "kubectl get pods -n tiptop -w"
echo ""
echo "✅ Diagnostic and fix script completed!"
